@echo off
echo ============================================================
echo 🚀 STARTING ALLORA HYBRID SERVER
echo ============================================================
echo.
echo This will start the Allora backend server with:
echo - Gevent WSGI Server (optimal for Windows)
echo - Full WebSocket support via Flask-SocketIO
echo - 10,000+ concurrent connections capability
echo - Production-ready performance
echo.
echo Server will be available at: http://127.0.0.1:5000
echo Health check: http://127.0.0.1:5000/api/health
echo SocketIO test: http://127.0.0.1:5000/socketio-test
echo.
echo Press Ctrl+C to stop the server
echo ============================================================
echo.

python run_hybrid_server.py

echo.
echo ============================================================
echo 🛑 SERVER STOPPED
echo ============================================================
pause
